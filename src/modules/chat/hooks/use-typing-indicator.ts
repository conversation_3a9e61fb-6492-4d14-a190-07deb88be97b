import { useEffect, useState, useCallback } from 'react';
import { createClient } from '@/lib/supabase/client';
import { CHAT_CONFIG } from '../constants';

export function useTypingIndicator(conversationId: string, userId: string) {
  const [typingUsers, setTypingUsers] = useState<string[]>([]);
  const [isTyping, setIsTyping] = useState(false);

  const startTyping = useCallback(() => {
    if (!isTyping) {
      setIsTyping(true);
      const supabase = createClient();
      supabase.channel(`typing:${conversationId}`)
        .send({
          type: 'broadcast',
          event: 'typing',
          payload: { user_id: userId, typing: true }
        });
    }
  }, [conversationId, userId, isTyping]);

  const stopTyping = useCallback(() => {
    if (isTyping) {
      setIsTyping(false);
      const supabase = createClient();
      supabase.channel(`typing:${conversationId}`)
        .send({
          type: 'broadcast',
          event: 'typing',
          payload: { user_id: userId, typing: false }
        });
    }
  }, [conversationId, userId, isTyping]);

  useEffect(() => {
    const supabase = createClient();

    const channel = supabase
      .channel(`typing:${conversationId}`)
      .on('broadcast', { event: 'typing' }, (payload) => {
        const { user_id, typing } = payload.payload;
        if (user_id !== userId) {
          setTypingUsers(prev =>
            typing
              ? [...prev.filter(id => id !== user_id), user_id]
              : prev.filter(id => id !== user_id)
          );
        }
      })
      .subscribe();

    return () => {
      supabase.removeChannel(channel);
    };
  }, [conversationId, userId]);

  // Auto-stop typing after timeout
  useEffect(() => {
    if (isTyping) {
      const timeout = setTimeout(stopTyping, CHAT_CONFIG.TYPING_TIMEOUT);
      return () => clearTimeout(timeout);
    }
  }, [isTyping, stopTyping]);

  return {
    typingUsers,
    startTyping,
    stopTyping,
  };
}
