import { useEffect } from 'react';
import {
  useChatMessages,
  useChatLoading,
  useChatError,
  useChatStore
} from '../store';
import { useRealtimeMessages } from './use-realtime-messages';
import { useTypingIndicator } from './use-typing-indicator';

export function useChat(conversationId: string, userId: string) {
  // Reactive state
  const messages = useChatMessages(conversationId);
  const loading = useChatLoading(conversationId);
  const error = useChatError();

  // Direct action access
  const {
    loadMessages,
    sendMessage,
    setCurrentConversation,
    clearError
  } = useChatStore.getState();

  // Real-time subscriptions
  const { isConnected } = useRealtimeMessages(conversationId);
  const { typingUsers, startTyping, stopTyping } = useTypingIndicator(conversationId, userId);

  // Load messages on mount
  useEffect(() => {
    if (conversationId && !messages.length && !loading) {
      loadMessages(conversationId);
    }
  }, [conversationId, messages.length, loading, loadMessages]);

  // Set current conversation
  useEffect(() => {
    setCurrentConversation(conversationId);
  }, [conversationId, setCurrentConversation]);

  return {
    messages,
    loading,
    error,
    isConnected,
    typingUsers,
    sendMessage: (content: string) => sendMessage(conversationId, content),
    startTyping,
    stopTyping,
    clearError,
  };
}
