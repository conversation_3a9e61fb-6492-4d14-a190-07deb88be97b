import { useEffect, useState } from 'react';
import { createClient } from '@/lib/supabase/client';
import { Message } from '../types';
import { useChatStore } from '../store';

export function useRealtimeMessages(conversationId: string) {
  const [isConnected, setIsConnected] = useState(false);
  const { addMessage, updateMessage } = useChatStore.getState();

  useEffect(() => {
    const supabase = createClient();

    const channel = supabase
      .channel(`messages:${conversationId}`)
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'messages',
          filter: `conversation_id=eq.${conversationId}`,
        },
        (payload) => {
          const newMessage = payload.new as Message;
          addMessage(conversationId, newMessage);
        }
      )
      .on(
        'postgres_changes',
        {
          event: 'UPDATE',
          schema: 'public',
          table: 'messages',
          filter: `conversation_id=eq.${conversationId}`,
        },
        (payload) => {
          const updatedMessage = payload.new as Message;
          updateMessage(conversationId, updatedMessage);
        }
      )
      .subscribe((status) => {
        setIsConnected(status === 'SUBSCRIBED');
      });

    return () => {
      supabase.removeChannel(channel);
    };
  }, [conversationId, addMessage, updateMessage]);

  return { isConnected };
}
