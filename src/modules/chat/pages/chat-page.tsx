'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { ChatLayout } from '../components/layout/chat-layout';
import { useAuthStore } from '@/stores/auth-store';

interface ChatPageProps {
  conversationId: string;
}

export function ChatPage({ conversationId }: ChatPageProps) {
  const router = useRouter();
  const { user } = useAuthStore();

  useEffect(() => {
    if (!user) {
      router.push('/auth/login');
      return;
    }

    if (!conversationId) {
      router.push('/dashboard');
      return;
    }
  }, [user, conversationId, router]);

  if (!user || !conversationId) {
    return (
      <div className="flex h-screen items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return <ChatLayout conversationId={conversationId} />;
}
