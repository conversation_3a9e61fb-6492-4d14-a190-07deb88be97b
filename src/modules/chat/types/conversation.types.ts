import { Message } from './message.types';

export interface ConversationDetails {
  id: string;
  type: 'direct' | 'group';
  participants: ConversationParticipant[];
  subject_id?: string;
  created_at: string;
  last_message?: Message;
  unread_count?: number;
}

export interface ConversationParticipant {
  id: string;
  conversation_id: string;
  user_id: string;
  joined_at: string;
  last_read_at?: string;
  user: {
    id: string;
    first_name: string;
    last_name: string;
    avatar_url?: string;
  };
}
