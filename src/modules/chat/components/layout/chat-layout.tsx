'use client';

import { ReactNode } from 'react';
import { <PERSON><PERSON><PERSON>ead<PERSON> } from './chat-header';
import { MessageList } from '../messages/message-list';
import { MessageInput } from '../input/message-input';
import { TypingIndicator } from '../messages/typing-indicator';
import { useChat } from '../../hooks/use-chat';
import { useAuthStore } from '@/stores/auth-store';

interface ChatLayoutProps {
  conversationId: string;
  children?: ReactNode;
}

export function ChatLayout({ conversationId }: ChatLayoutProps) {
  const { user } = useAuthStore();
  const {
    messages,
    loading,
    error,
    isConnected,
    typingUsers,
    sendMessage,
    startTyping,
    stopTyping,
    clearError,
  } = useChat(conversationId, user?.id || '');

  if (loading && !messages.length) {
    return (
      <div className="flex h-screen items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2"></div>
          <p className="text-gray-600">Loading chat...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-screen bg-gray-50">
      {/* Header */}
      <ChatHeader
        conversationId={conversationId}
        isConnected={isConnected}
      />

      {/* Error Banner */}
      {error && (
        <div className="bg-red-50 border-l-4 border-red-400 p-4">
          <div className="flex justify-between items-center">
            <p className="text-red-700">{error}</p>
            <button
              onClick={clearError}
              className="text-red-400 hover:text-red-600"
            >
              ×
            </button>
          </div>
        </div>
      )}

      {/* Messages */}
      <div className="flex-1 overflow-hidden">
        <MessageList
          messages={messages}
          currentUserId={user?.id || ''}
          loading={loading}
        />
      </div>

      {/* Typing Indicator */}
      {typingUsers.length > 0 && (
        <TypingIndicator userIds={typingUsers} />
      )}

      {/* Message Input */}
      <MessageInput
        onSendMessage={sendMessage}
        onStartTyping={startTyping}
        onStopTyping={stopTyping}
        disabled={!isConnected}
      />
    </div>
  );
}
