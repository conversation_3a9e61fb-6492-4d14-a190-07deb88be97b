'use client';

import { ArrowLeft, Users, Phone, Video, MoreVertical } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useRouter } from 'next/navigation';
import { useState, useEffect } from 'react';
import { ConversationDetails } from '../../types';
import { ChatClient } from '../../api';

interface ChatHeaderProps {
  conversationId: string;
  isConnected: boolean;
}

export function ChatHeader({ conversationId, isConnected }: ChatHeaderProps) {
  const router = useRouter();
  const [conversation, setConversation] = useState<ConversationDetails | null>(null);

  useEffect(() => {
    const loadConversation = async () => {
      const { data } = await ChatClient.getConversationDetails(conversationId);
      if (data) setConversation(data);
    };
    loadConversation();
  }, [conversationId]);

  const getHeaderTitle = () => {
    if (!conversation) return 'Loading...';

    if (conversation.type === 'group') {
      return conversation.subject_id ? 'Group Chat' : 'Group';
    }

    // For direct chats, show other participant's name
    const otherParticipant = conversation.participants.find(p => p.user_id !== conversation.participants[0].user_id);
    return otherParticipant
      ? `${otherParticipant.user.first_name} ${otherParticipant.user.last_name}`
      : 'Direct Chat';
  };

  const getSubtitle = () => {
    if (!conversation) return '';

    if (conversation.type === 'group') {
      return `${conversation.participants.length} participants`;
    }

    return isConnected ? 'Online' : 'Connecting...';
  };

  return (
    <div className="bg-white border-b border-gray-200 px-4 py-3">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          {/* Back Button */}
          <Button
            variant="ghost"
            size="icon"
            onClick={() => router.back()}
            className="md:hidden"
          >
            <ArrowLeft className="h-5 w-5" />
          </Button>

          {/* Conversation Info */}
          <div className="flex items-center space-x-3">
            <div className="relative">
              <div className="w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center">
                {conversation?.type === 'group' ? (
                  <Users className="h-5 w-5 text-white" />
                ) : (
                  <div className="w-8 h-8 bg-gray-300 rounded-full"></div>
                )}
              </div>
              {isConnected && (
                <div className="absolute -bottom-1 -right-1 w-3 h-3 bg-green-500 rounded-full border-2 border-white"></div>
              )}
            </div>

            <div>
              <h1 className="font-semibold text-gray-900">{getHeaderTitle()}</h1>
              <p className="text-sm text-gray-500">{getSubtitle()}</p>
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex items-center space-x-2">
          <Button variant="ghost" size="icon" className="hidden md:flex">
            <Phone className="h-5 w-5" />
          </Button>
          <Button variant="ghost" size="icon" className="hidden md:flex">
            <Video className="h-5 w-5" />
          </Button>
          <Button variant="ghost" size="icon">
            <MoreVertical className="h-5 w-5" />
          </Button>
        </div>
      </div>
    </div>
  );
}
