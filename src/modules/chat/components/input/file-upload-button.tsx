'use client';

import { useRef, ReactNode } from 'react';
import { Button } from '@/components/ui/button';
import { CHAT_CONFIG } from '../../constants';

interface FileUploadButtonProps {
  onFileSelect: (file: File) => void;
  accept?: string;
  disabled?: boolean;
  children: ReactNode;
}

export function FileUploadButton({
  onFileSelect,
  accept = "*/*",
  disabled = false,
  children
}: FileUploadButtonProps) {
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Validate file size
    if (file.size > CHAT_CONFIG.MAX_FILE_SIZE) {
      alert(`File size must be less than ${CHAT_CONFIG.MAX_FILE_SIZE / (1024 * 1024)}MB`);
      return;
    }

    // Validate file type
    if (!CHAT_CONFIG.ALLOWED_FILE_TYPES.includes(file.type)) {
      alert('File type not supported');
      return;
    }

    onFileSelect(file);

    // Reset input
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  return (
    <>
      <input
        ref={fileInputRef}
        type="file"
        accept={accept}
        onChange={handleFileSelect}
        className="hidden"
        disabled={disabled}
      />
      <Button
        variant="ghost"
        size="icon"
        onClick={() => fileInputRef.current?.click()}
        disabled={disabled}
        className="h-10 w-10"
      >
        {children}
      </Button>
    </>
  );
}
