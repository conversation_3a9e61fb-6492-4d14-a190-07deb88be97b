'use client';

import { Download, FileText, File } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { MessageAttachment } from '../../types';
import { formatFileSize } from '../../utils/file-utils';

interface FileAttachmentProps {
  attachment: MessageAttachment;
}

export function FileAttachment({ attachment }: FileAttachmentProps) {
  const handleDownload = () => {
    const link = document.createElement('a');
    link.href = attachment.file_url;
    link.download = attachment.file_name;
    link.target = '_blank';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const getFileIcon = () => {
    if (attachment.file_type.includes('pdf')) {
      return <FileText className="h-8 w-8 text-red-500" />;
    }
    return <File className="h-8 w-8 text-gray-500" />;
  };

  return (
    <div className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg border">
      {getFileIcon()}

      <div className="flex-1 min-w-0">
        <p className="text-sm font-medium text-gray-900 truncate">
          {attachment.file_name}
        </p>
        <p className="text-xs text-gray-500">
          {formatFileSize(attachment.file_size)}
        </p>
      </div>

      <Button
        variant="ghost"
        size="icon"
        onClick={handleDownload}
        className="h-8 w-8"
      >
        <Download className="h-4 w-4" />
      </Button>
    </div>
  );
}
