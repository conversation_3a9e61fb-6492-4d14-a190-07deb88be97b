'use client';

import { Message } from '../../types';
import { formatMessageTime } from '../../utils/date-utils';
import { FileAttachment } from './file-attachment';
import { ImageAttachment } from './image-attachment';

interface MessageBubbleProps {
  message: Message;
  isOwn: boolean;
  showAvatar: boolean;
}

export function MessageBubble({ message, isOwn, showAvatar }: MessageBubbleProps) {
  const renderMessageContent = () => {
    switch (message.message_type) {
      case 'text':
        return (
          <p className="text-sm whitespace-pre-wrap break-words">
            {message.content}
          </p>
        );

      case 'image':
        return (
          <div>
            {message.attachments?.map(attachment => (
              <ImageAttachment
                key={attachment.id}
                attachment={attachment}
              />
            ))}
            {message.content && (
              <p className="text-sm mt-2 whitespace-pre-wrap break-words">
                {message.content}
              </p>
            )}
          </div>
        );

      case 'file':
        return (
          <div>
            {message.attachments?.map(attachment => (
              <FileAttachment
                key={attachment.id}
                attachment={attachment}
              />
            ))}
            {message.content && (
              <p className="text-sm mt-2 whitespace-pre-wrap break-words">
                {message.content}
              </p>
            )}
          </div>
        );

      default:
        return <p className="text-sm text-gray-500">Unsupported message type</p>;
    }
  };

  return (
    <div className={`flex ${isOwn ? 'justify-end' : 'justify-start'} mb-2`}>
      <div className={`flex max-w-xs lg:max-w-md ${isOwn ? 'flex-row-reverse' : 'flex-row'}`}>
        {/* Avatar */}
        {showAvatar && !isOwn && (
          <div className="w-8 h-8 rounded-full bg-gray-300 flex-shrink-0 mr-2 flex items-center justify-center">
            {message.sender?.avatar_url ? (
              <img
                src={message.sender.avatar_url}
                alt={`${message.sender.first_name} ${message.sender.last_name}`}
                className="w-8 h-8 rounded-full object-cover"
              />
            ) : (
              <span className="text-xs font-medium text-gray-600">
                {message.sender?.first_name?.[0]}{message.sender?.last_name?.[0]}
              </span>
            )}
          </div>
        )}

        {/* Message Content */}
        <div className={`${showAvatar && !isOwn ? '' : 'ml-10'}`}>
          {/* Sender Name (for group chats) */}
          {!isOwn && showAvatar && (
            <p className="text-xs text-gray-500 mb-1">
              {message.sender?.first_name} {message.sender?.last_name}
            </p>
          )}

          {/* Message Bubble */}
          <div
            className={`
              px-4 py-2 rounded-lg
              ${isOwn
                ? 'bg-blue-600 text-white'
                : 'bg-white border border-gray-200 text-gray-900'
              }
            `}
          >
            {renderMessageContent()}

            {/* Timestamp */}
            <p className={`text-xs mt-1 ${isOwn ? 'text-blue-100' : 'text-gray-500'}`}>
              {formatMessageTime(message.created_at)}
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
