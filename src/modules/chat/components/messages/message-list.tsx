'use client';

import { useEffect, useRef } from 'react';
import { Message } from '../../types';
import { MessageBubble } from './message-bubble';
import { DateSeparator } from './date-separator';
import { LoadingSpinner } from '../common/loading-spinner';

interface MessageListProps {
  messages: Message[];
  currentUserId: string;
  loading: boolean;
}

export function MessageList({ messages, currentUserId, loading }: MessageListProps) {
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  // Auto-scroll to bottom for new messages
  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [messages]);

  const groupMessagesByDate = (messages: Message[]) => {
    const groups: { date: string; messages: Message[] }[] = [];
    let currentDate = '';

    messages.forEach(message => {
      const messageDate = new Date(message.created_at).toDateString();

      if (messageDate !== currentDate) {
        currentDate = messageDate;
        groups.push({ date: messageDate, messages: [message] });
      } else {
        groups[groups.length - 1].messages.push(message);
      }
    });

    return groups;
  };

  const messageGroups = groupMessagesByDate([...messages].reverse());

  if (loading && !messages.length) {
    return (
      <div className="flex-1 flex items-center justify-center">
        <LoadingSpinner />
      </div>
    );
  }

  if (!messages.length) {
    return (
      <div className="flex-1 flex items-center justify-center">
        <div className="text-center text-gray-500">
          <p>No messages yet</p>
          <p className="text-sm">Start the conversation!</p>
        </div>
      </div>
    );
  }

  return (
    <div
      ref={containerRef}
      className="flex-1 overflow-y-auto px-4 py-4 space-y-4"
    >
      {messageGroups.map((group, groupIndex) => (
        <div key={group.date}>
          <DateSeparator date={group.date} />

          <div className="space-y-2">
            {group.messages.map((message, messageIndex) => {
              const isOwn = message.sender_id === currentUserId;
              const showAvatar = !isOwn && (
                messageIndex === group.messages.length - 1 ||
                group.messages[messageIndex + 1]?.sender_id !== message.sender_id
              );

              return (
                <MessageBubble
                  key={message.id}
                  message={message}
                  isOwn={isOwn}
                  showAvatar={showAvatar}
                />
              );
            })}
          </div>
        </div>
      ))}

      <div ref={messagesEndRef} />
    </div>
  );
}
