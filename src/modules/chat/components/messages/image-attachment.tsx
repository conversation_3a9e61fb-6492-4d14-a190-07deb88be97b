'use client';

import { useState } from 'react';
import { MessageAttachment } from '../../types';
import { Dialog, DialogContent } from '@/components/ui/dialog';

interface ImageAttachmentProps {
  attachment: MessageAttachment;
}

export function ImageAttachment({ attachment }: ImageAttachmentProps) {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <>
      <div
        className="cursor-pointer rounded-lg overflow-hidden max-w-xs"
        onClick={() => setIsOpen(true)}
      >
        <img
          src={attachment.file_url}
          alt={attachment.file_name}
          className="w-full h-auto object-cover hover:opacity-90 transition-opacity"
          loading="lazy"
        />
      </div>

      <Dialog open={isOpen} onOpenChange={setIsOpen}>
        <DialogContent className="max-w-4xl max-h-[90vh] p-0">
          <img
            src={attachment.file_url}
            alt={attachment.file_name}
            className="w-full h-auto object-contain"
          />
        </DialogContent>
      </Dialog>
    </>
  );
}
