import { createClient } from '@/lib/supabase/server';
import { CHAT_CONFIG } from '../constants';

export async function uploadChatFile(
  file: File,
  conversationId: string,
  userId: string
): Promise<{ data: any | null; error: string | null }> {
  try {
    const supabase = await createClient();

    // Verify user is participant in conversation
    const { data: participant } = await supabase
      .from('conversation_participants')
      .select('id')
      .eq('conversation_id', conversationId)
      .eq('user_id', userId)
      .single();

    if (!participant) {
      return { data: null, error: 'Access denied' };
    }

    // Generate unique filename
    const fileExt = file.name.split('.').pop();
    const fileName = `${Date.now()}-${Math.random().toString(36).substring(2)}.${fileExt}`;
    const filePath = `chat-files/${conversationId}/${fileName}`;

    // Upload to Supabase Storage
    const { data: uploadData, error: uploadError } = await supabase.storage
      .from('chat-files')
      .upload(filePath, file, {
        cacheControl: '3600',
        upsert: false
      });

    if (uploadError) {
      return { data: null, error: uploadError.message };
    }

    // Get public URL
    const { data: urlData } = supabase.storage
      .from('chat-files')
      .getPublicUrl(filePath);

    return {
      data: {
        file_url: urlData.publicUrl,
        file_name: file.name,
        file_type: file.type,
        file_size: file.size,
        storage_path: filePath,
      },
      error: null,
    };
  } catch (error) {
    console.error('File upload error:', error);
    return { data: null, error: 'Upload failed' };
  }
}
