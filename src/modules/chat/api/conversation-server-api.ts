import { createClient } from '@/lib/supabase/server';
import { ConversationDetails } from '../types';

export async function getConversationDetails(
  conversationId: string,
  userId: string
): Promise<{ data: ConversationDetails | null; error: string | null }> {
  try {
    const supabase = await createClient();

    // Verify user is participant in conversation
    const { data: participant } = await supabase
      .from('conversation_participants')
      .select('id')
      .eq('conversation_id', conversationId)
      .eq('user_id', userId)
      .single();

    if (!participant) {
      return { data: null, error: 'Access denied' };
    }

    // Get conversation details with participants
    const { data: conversation, error } = await supabase
      .from('conversations')
      .select(`
        id,
        type,
        subject_id,
        created_at,
        participants:conversation_participants(
          id,
          conversation_id,
          user_id,
          joined_at,
          last_read_at,
          user:users!user_id(
            id,
            first_name,
            last_name,
            avatar_url
          )
        )
      `)
      .eq('id', conversationId)
      .single();

    if (error) {
      return { data: null, error: error.message };
    }

    return { data: conversation as unknown as ConversationDetails, error: null };
  } catch (error) {
    console.error('Get conversation details error:', error);
    return { data: null, error: 'Failed to fetch conversation details' };
  }
}
