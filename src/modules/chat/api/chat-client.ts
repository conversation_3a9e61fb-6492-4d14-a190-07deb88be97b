import { Message, SendMessageRequest, ConversationDetails } from '../types';

export class ChatClient {
  static async getMessages(
    conversationId: string,
    limit = 50,
    offset = 0
  ): Promise<{ data: Message[] | null; error: string | null }> {
    try {
      const params = new URLSearchParams({
        limit: limit.toString(),
        offset: offset.toString(),
      });

      const response = await fetch(`/api/chat/messages/${conversationId}?${params}`, {
        credentials: 'include',
      });

      if (!response.ok) {
        const errorData = await response.json();
        return { data: null, error: errorData.error || 'Failed to fetch messages' };
      }

      const data = await response.json();
      return { data, error: null };
    } catch (error) {
      console.error('Error fetching messages:', error);
      return { data: null, error: 'Failed to fetch messages' };
    }
  }

  static async sendMessage(
    request: SendMessageRequest
  ): Promise<{ data: Message | null; error: string | null }> {
    try {
      const response = await fetch('/api/chat/messages/send', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify(request),
      });

      if (!response.ok) {
        const errorData = await response.json();
        return { data: null, error: errorData.error || 'Failed to send message' };
      }

      const data = await response.json();
      return { data, error: null };
    } catch (error) {
      console.error('Error sending message:', error);
      return { data: null, error: 'Failed to send message' };
    }
  }

  static async getConversationDetails(
    conversationId: string
  ): Promise<{ data: ConversationDetails | null; error: string | null }> {
    try {
      const response = await fetch(`/api/conversations/${conversationId}`, {
        credentials: 'include',
      });

      if (!response.ok) {
        const errorData = await response.json();
        return { data: null, error: errorData.error || 'Failed to fetch conversation' };
      }

      const data = await response.json();
      return { data, error: null };
    } catch (error) {
      console.error('Error fetching conversation:', error);
      return { data: null, error: 'Failed to fetch conversation' };
    }
  }
}
