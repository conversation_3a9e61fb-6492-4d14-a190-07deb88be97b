import { CHAT_CONFIG } from '../constants';

export interface FileUploadProgress {
  loaded: number;
  total: number;
  percentage: number;
}

export interface FileUploadResult {
  file_url: string;
  file_name: string;
  file_type: string;
  file_size: number;
}

export class FileUploadClient {
  static async uploadFile(
    file: File,
    conversationId: string,
    onProgress?: (progress: FileUploadProgress) => void
  ): Promise<{ data: FileUploadResult | null; error: string | null }> {
    try {
      // Validate file
      if (file.size > CHAT_CONFIG.MAX_FILE_SIZE) {
        return { data: null, error: 'File too large' };
      }

      if (!CHAT_CONFIG.ALLOWED_FILE_TYPES.includes(file.type)) {
        return { data: null, error: 'File type not supported' };
      }

      const formData = new FormData();
      formData.append('file', file);
      formData.append('conversation_id', conversationId);

      const response = await fetch('/api/chat/upload', {
        method: 'POST',
        body: formData,
        credentials: 'include',
      });

      if (!response.ok) {
        const errorData = await response.json();
        return { data: null, error: errorData.error || 'Upload failed' };
      }

      const result = await response.json();
      return { data: result, error: null };
    } catch (error) {
      console.error('File upload error:', error);
      return { data: null, error: 'Upload failed' };
    }
  }
}
