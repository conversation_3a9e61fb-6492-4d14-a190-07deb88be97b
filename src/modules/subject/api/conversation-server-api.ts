import { createClient } from '@/lib/supabase/server';
import { ConversationData, CreateChatResponse } from '../types';

/**
 * Server-side API functions for conversation operations
 */

/**
 * Create or retrieve direct conversation between current user and another user
 */
export async function getOrCreateDirectChat(
   userId: string,
   participantId: string
): Promise<{ data: CreateChatResponse | null; error: string | null }> {
   try {
      const supabase = await createClient();

      // Validate that both users exist
      const { data: participantData, error: participantError } = await supabase
         .from('users')
         .select('id')
         .eq('id', participantId)
         .single();

      if (participantError || !participantData) {
         return { data: null, error: 'Participant not found' };
      }

      // Check if a direct conversation already exists between these users
      // First, find conversations where both users are participants
      const { data: userConversations, error: userConvError } = await supabase
         .from('conversation_participants')
         .select('conversation_id')
         .eq('user_id', userId);

      if (userConvError) {
         return { data: null, error: 'Failed to check existing conversations' };
      }

      const { data: participantConversations, error: participantConvError } = await supabase
         .from('conversation_participants')
         .select('conversation_id')
         .eq('user_id', participantId);

      if (participantConvError) {
         return { data: null, error: 'Failed to check existing conversations' };
      }

      // Find common conversation IDs
      const userConvIds = userConversations?.map(c => c.conversation_id) || [];
      const participantConvIds = participantConversations?.map(c => c.conversation_id) || [];
      const commonConvIds = userConvIds.filter(id => participantConvIds.includes(id));

      let existingConversation = null;
      if (commonConvIds.length > 0) {
         // Check if any of these common conversations are direct type
         const { data: directConv, error: directError } = await supabase
            .from('conversations')
            .select('id')
            .eq('type', 'direct')
            .in('id', commonConvIds)
            .single();

         if (!directError && directConv) {
            existingConversation = directConv;
         }
      }

      if (existingConversation) {
         // Return existing conversation
         return {
            data: {
               conversation_id: existingConversation.id,
               redirect_url: `/chat/${existingConversation.id}`,
            },
            error: null,
         };
      }

      // Create new direct conversation
      const { data: newConversation, error: createError } = await supabase
         .from('conversations')
         .insert({
            type: 'direct',
            created_by: userId,
            created_at: new Date().toISOString(),
         })
         .select('id')
         .single();

      if (createError || !newConversation) {
         console.error('Error creating conversation:', createError);
         return { data: null, error: 'Failed to create conversation' };
      }

      // Add participants to the conversation
      const { error: participantsError } = await supabase.from('conversation_participants').insert([
         { conversation_id: newConversation.id, user_id: userId },
         { conversation_id: newConversation.id, user_id: participantId },
      ]);

      if (participantsError) {
         console.error('Error adding participants:', participantsError);
         // Clean up the conversation if participants couldn't be added
         await supabase.from('conversations').delete().eq('id', newConversation.id);
         return { data: null, error: 'Failed to create conversation' };
      }

      return {
         data: {
            conversation_id: newConversation.id,
            redirect_url: `/chat/${newConversation.id}`,
         },
         error: null,
      };
   } catch (error) {
      console.error('Database error creating direct chat:', error);
      return { data: null, error: 'Failed to create chat' };
   }
}

/**
 * Get or create group chat for a specific subject
 */
export async function getGroupChatBySubject(
   subjectId: string,
   userId: string
): Promise<{ data: ConversationData | null; error: string | null }> {
   try {
      const supabase = await createClient();

      // Verify user is enrolled in this subject
      const { data: enrollment, error: enrollmentError } = await supabase
         .from('user_subjects')
         .select('id')
         .eq('user_id', userId)
         .eq('subject_id', subjectId)
         .eq('is_active', true)
         .single();

      if (enrollmentError || !enrollment) {
         return { data: null, error: 'Subject not found or access denied' };
      }

      // Check if group conversation already exists for this subject
      const { data: existingConversation, error: existingError } = await supabase
         .from('conversations')
         .select(
            `
            id,
            type,
            subject_id,
            created_at,
            participants:conversation_participants(user_id)
         `
         )
         .eq('type', 'group')
         .eq('subject_id', subjectId)
         .single();

      if (!existingError && existingConversation) {
         // Transform participants to array of user IDs
         const participants = existingConversation.participants?.map((p: any) => p.user_id) || [];
         return {
            data: {
               ...existingConversation,
               participants,
            },
            error: null,
         };
      }

      // Get all enrolled students for this subject to create group chat
      const { data: studentsData, error: studentsError } = await supabase
         .from('user_subjects')
         .select('user_id')
         .eq('subject_id', subjectId)
         .eq('is_active', true);

      if (studentsError || !studentsData) {
         return { data: null, error: 'Failed to load subject participants' };
      }

      const participants = studentsData.map(student => student.user_id);

      // Create new group conversation
      const { data: newConversation, error: createError } = await supabase
         .from('conversations')
         .insert({
            type: 'group',
            subject_id: subjectId,
            created_by: userId,
            created_at: new Date().toISOString(),
         })
         .select('id, type, subject_id, created_at')
         .single();

      if (createError || !newConversation) {
         console.error('Error creating group conversation:', createError);
         return { data: null, error: 'Failed to create group conversation' };
      }

      // Add all participants to the conversation
      const participantInserts = participants.map(participantId => ({
         conversation_id: newConversation.id,
         user_id: participantId,
      }));

      const { error: participantsError } = await supabase
         .from('conversation_participants')
         .insert(participantInserts);

      if (participantsError) {
         console.error('Error adding participants to group:', participantsError);
         // Clean up the conversation if participants couldn't be added
         await supabase.from('conversations').delete().eq('id', newConversation.id);
         return { data: null, error: 'Failed to create group conversation' };
      }

      // Return conversation data with participants
      const conversationWithParticipants = {
         ...newConversation,
         participants,
      };

      return { data: conversationWithParticipants, error: null };
   } catch (error) {
      console.error('Database error getting group chat:', error);
      return { data: null, error: 'Failed to load group chat' };
   }
}

/**
 * Create a new conversation
 */
export async function createConversation(
   type: 'direct' | 'group',
   participants: string[],
   createdBy: string,
   subjectId?: string
): Promise<{ data: ConversationData | null; error: string | null }> {
   try {
      const supabase = await createClient();

      // Validate participants exist
      const { data: participantData, error: participantError } = await supabase
         .from('users')
         .select('id')
         .in('id', participants);

      if (participantError || !participantData || participantData.length !== participants.length) {
         return { data: null, error: 'One or more participants not found' };
      }

      // Create conversation
      const { data: conversation, error: createError } = await supabase
         .from('conversations')
         .insert({
            type,
            subject_id: subjectId,
            created_by: createdBy,
            created_at: new Date().toISOString(),
         })
         .select('id, type, subject_id, created_at')
         .single();

      if (createError || !conversation) {
         console.error('Error creating conversation:', createError);
         return { data: null, error: 'Failed to create conversation' };
      }

      // Add participants to the conversation
      const participantInserts = participants.map(participantId => ({
         conversation_id: conversation.id,
         user_id: participantId,
      }));

      const { error: participantsError } = await supabase
         .from('conversation_participants')
         .insert(participantInserts);

      if (participantsError) {
         console.error('Error adding participants:', participantsError);
         // Clean up the conversation if participants couldn't be added
         await supabase.from('conversations').delete().eq('id', conversation.id);
         return { data: null, error: 'Failed to create conversation' };
      }

      // Return conversation data with participants
      const conversationWithParticipants = {
         ...conversation,
         participants,
      };

      return { data: conversationWithParticipants, error: null };
   } catch (error) {
      console.error('Database error creating conversation:', error);
      return { data: null, error: 'Failed to create conversation' };
   }
}