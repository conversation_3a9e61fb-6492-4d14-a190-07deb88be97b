import { z } from 'zod';

export const createChatSchema = z.object({
   participant_id: z.string().min(1, 'Participant ID is required'),
});

export const subjectParamsSchema = z.object({
  id: z.string().min(1, 'Subject ID is required'),
});

export const createConversationSchema = z.object({
  type: z.enum(['direct', 'group']),
  participants: z.array(z.string().uuid()).min(1, 'At least one participant required'),
  subject_id: z.string().uuid().optional(),
});

export const subjectStatsParamsSchema = z.object({
  subjectId: z.string().min(1, 'Subject ID is required'),
  userId: z.string().uuid('Invalid user ID format'),
});