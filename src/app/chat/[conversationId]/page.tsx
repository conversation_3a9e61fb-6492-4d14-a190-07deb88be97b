import { ChatPage } from '@/modules/chat/pages/chat-page';

interface PageProps {
  params: Promise<{
    conversationId: string;
  }>;
}

export default async function Page(props: PageProps) {
  const params = await props.params;
  return <ChatPage conversationId={params.conversationId} />;
}

export async function generateMetadata(props: PageProps) {
  return {
    title: 'Chat - College Platform',
    description: 'Real-time messaging with classmates',
  };
}
