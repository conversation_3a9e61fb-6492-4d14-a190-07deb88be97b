import { NextRequest, NextResponse } from 'next/server';
import { withAuth } from '@/lib/api-auth';
import { uploadChatFile } from '@/modules/chat/api/file-upload-server-api';

export const POST = withAuth(async (request: NextRequest, { user }) => {
  try {
    const formData = await request.formData();
    const file = formData.get('file') as File;
    const conversationId = formData.get('conversation_id') as string;

    if (!file || !conversationId) {
      return NextResponse.json(
        { error: 'File and conversation_id are required' },
        { status: 400 }
      );
    }

    const { data, error } = await uploadChatFile(file, conversationId, user.userId);

    if (error) {
      return NextResponse.json({ error }, { status: 500 });
    }

    return NextResponse.json(data);
  } catch (error) {
    console.error('File upload error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
});
