import { NextRequest, NextResponse } from 'next/server';
import { withAuth } from '@/lib/api-auth';
import { sendMessage } from '@/modules/chat/api/message-server-api';
import { sendMessageSchema } from '@/modules/chat/schemas';

export const POST = withAuth(async (request: NextRequest, { user }) => {
  try {
    const body = await request.json();

    const validation = sendMessageSchema.safeParse(body);
    if (!validation.success) {
      return NextResponse.json(
        { error: 'Invalid request data', details: validation.error.issues },
        { status: 400 }
      );
    }

    const { data, error } = await sendMessage(validation.data, user.userId);

    if (error) {
      return NextResponse.json({ error }, { status: 500 });
    }

    return NextResponse.json(data);
  } catch (error) {
    console.error('Send message error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
});
