# Module 4: Real-time Chat System - Comprehensive Implementation Plan

## 📋 Current State Analysis

### ✅ Already Implemented
- **Conversation APIs**: Direct and group chat creation/retrieval
- **Database Schema**: Complete tables for conversations, messages, attachments
- **Navigation Components**: Chat buttons and group chat access
- **Authentication**: Protected API routes with user context
- **Project Structure**: Modular architecture with established patterns

### 🔧 What Needs Implementation
- **Complete Chat Module**: Following established modular patterns
- **Real-time Messaging**: Supabase real-time subscriptions
- **File Upload System**: Secure file handling with progress tracking
- **Chat UI Components**: WhatsApp-like interface
- **Message Management**: Pagination, search, and history
- **Mobile Responsiveness**: Touch-optimized chat experience

---

## 🏗️ Implementation Plan

### **Phase 1: Foundation & Module Setup (Days 1-2)**

#### **Day 1: Chat Module Structure & Core Types**

**Task 1.1: Create Chat Module Structure**
```bash
src/modules/chat/
├── components/          # All chat UI components
│   ├── layout/         # Chat layout components
│   ├── messages/       # Message-related components
│   ├── input/          # Message input and file upload
│   └── common/         # Shared chat components
├── pages/              # Chat page components
├── api/                # Chat API client functions
├── hooks/              # Real-time hooks and chat logic
├── types/              # Message, conversation, file types
├── schemas/            # Validation schemas
├── constants/          # Chat constants and configurations
├── utils/              # Chat utility functions
├── store/              # Chat state management (Zustand)
└── index.ts            # Module barrel exports
```

**Task 1.2: Define Core TypeScript Types**
- **File**: `src/modules/chat/types/message.types.ts`
  ```typescript
  export interface Message {
    id: string;
    conversation_id: string;
    sender_id: string;
    content: string | null;
    message_type: 'text' | 'file' | 'image';
    created_at: string;
    updated_at: string;
    sender?: {
      id: string;
      first_name: string;
      last_name: string;
      avatar_url?: string;
    };
    attachments?: MessageAttachment[];
  }

  export interface MessageAttachment {
    id: string;
    message_id: string;
    file_name: string;
    file_url: string;
    file_type: string;
    file_size: number;
    uploaded_at: string;
  }

  export interface SendMessageRequest {
    conversation_id: string;
    content?: string;
    message_type: 'text' | 'file' | 'image';
    file?: File;
  }
  ```

- **File**: `src/modules/chat/types/conversation.types.ts`
  ```typescript
  export interface ConversationDetails {
    id: string;
    type: 'direct' | 'group';
    participants: ConversationParticipant[];
    subject_id?: string;
    created_at: string;
    last_message?: Message;
    unread_count?: number;
  }

  export interface ConversationParticipant {
    id: string;
    conversation_id: string;
    user_id: string;
    joined_at: string;
    last_read_at?: string;
    user: {
      id: string;
      first_name: string;
      last_name: string;
      avatar_url?: string;
    };
  }
  ```

**Task 1.3: Chat Constants & Configuration**
- **File**: `src/modules/chat/constants/chat-constants.ts`
  ```typescript
  export const CHAT_CONFIG = {
    MAX_FILE_SIZE: 10 * 1024 * 1024, // 10MB
    ALLOWED_FILE_TYPES: [
      'image/jpeg', 'image/png', 'image/gif', 'image/webp',
      'application/pdf', 'text/plain', 'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
    ],
    MESSAGES_PER_PAGE: 50,
    TYPING_TIMEOUT: 3000,
    RECONNECT_INTERVAL: 5000,
  } as const;

  export const MESSAGE_TYPES = {
    TEXT: 'text',
    FILE: 'file',
    IMAGE: 'image',
  } as const;

  export const CHAT_ROUTES = {
    CHAT: '/chat',
    CONVERSATION: (id: string) => `/chat/${id}`,
  } as const;
  ```

#### **Day 2: Validation Schemas & API Foundation**

**Task 2.1: Validation Schemas**
- **File**: `src/modules/chat/schemas/message-schemas.ts`
  ```typescript
  import { z } from 'zod';
  import { CHAT_CONFIG, MESSAGE_TYPES } from '../constants';

  export const sendMessageSchema = z.object({
    conversation_id: z.string().uuid(),
    content: z.string().min(1).max(2000).optional(),
    message_type: z.enum([MESSAGE_TYPES.TEXT, MESSAGE_TYPES.FILE, MESSAGE_TYPES.IMAGE]),
  }).refine(
    (data) => data.message_type === MESSAGE_TYPES.TEXT ? !!data.content : true,
    { message: "Text messages must have content" }
  );

  export const getMessagesSchema = z.object({
    conversation_id: z.string().uuid(),
    limit: z.number().min(1).max(100).default(CHAT_CONFIG.MESSAGES_PER_PAGE),
    offset: z.number().min(0).default(0),
    before_id: z.string().uuid().optional(),
  });

  export const fileUploadSchema = z.object({
    file: z.instanceof(File)
      .refine(file => file.size <= CHAT_CONFIG.MAX_FILE_SIZE, 'File too large')
      .refine(file => CHAT_CONFIG.ALLOWED_FILE_TYPES.includes(file.type), 'Invalid file type'),
    conversation_id: z.string().uuid(),
  });
  ```

**Task 2.2: Server-side API Functions**
- **File**: `src/modules/chat/api/message-server-api.ts`
  ```typescript
  import { createClient } from '@/lib/supabase/server';
  import { Message, SendMessageRequest } from '../types';

  export async function getMessages(
    conversationId: string,
    userId: string,
    limit = 50,
    offset = 0
  ): Promise<{ data: Message[] | null; error: string | null }> {
    try {
      const supabase = await createClient();

      // Verify user is participant in conversation
      const { data: participant } = await supabase
        .from('conversation_participants')
        .select('id')
        .eq('conversation_id', conversationId)
        .eq('user_id', userId)
        .single();

      if (!participant) {
        return { data: null, error: 'Access denied' };
      }

      // Fetch messages with sender info
      const { data: messages, error } = await supabase
        .from('messages')
        .select(`
          id, conversation_id, sender_id, content, message_type, created_at, updated_at,
          sender:users!sender_id(id, first_name, last_name, avatar_url),
          attachments:message_attachments(*)
        `)
        .eq('conversation_id', conversationId)
        .order('created_at', { ascending: false })
        .range(offset, offset + limit - 1);

      if (error) {
        return { data: null, error: error.message };
      }

      return { data: messages || [], error: null };
    } catch (error) {
      return { data: null, error: 'Failed to fetch messages' };
    }
  }

  export async function sendMessage(
    request: SendMessageRequest,
    userId: string
  ): Promise<{ data: Message | null; error: string | null }> {
    try {
      const supabase = await createClient();

      // Verify user is participant
      const { data: participant } = await supabase
        .from('conversation_participants')
        .select('id')
        .eq('conversation_id', request.conversation_id)
        .eq('user_id', userId)
        .single();

      if (!participant) {
        return { data: null, error: 'Access denied' };
      }

      // Insert message
      const { data: message, error } = await supabase
        .from('messages')
        .insert({
          conversation_id: request.conversation_id,
          sender_id: userId,
          content: request.content,
          message_type: request.message_type,
        })
        .select(`
          id, conversation_id, sender_id, content, message_type, created_at, updated_at,
          sender:users!sender_id(id, first_name, last_name, avatar_url)
        `)
        .single();

      if (error) {
        return { data: null, error: error.message };
      }

      return { data: message, error: null };
    } catch (error) {
      return { data: null, error: 'Failed to send message' };
    }
  }
  ```

---

### **Phase 2: Real-time Infrastructure & State Management (Days 3-4)**

#### **Day 3: Real-time Subscriptions & Hooks**

**Task 3.1: Real-time Message Hook**
- **File**: `src/modules/chat/hooks/use-realtime-messages.ts`
  ```typescript
  import { useEffect, useState } from 'react';
  import { createClient } from '@/lib/supabase/client';
  import { Message } from '../types';
  import { useChatStore } from '../store';

  export function useRealtimeMessages(conversationId: string) {
    const [isConnected, setIsConnected] = useState(false);
    const { addMessage, updateMessage } = useChatStore.getState();

    useEffect(() => {
      const supabase = createClient();

      const channel = supabase
        .channel(`messages:${conversationId}`)
        .on(
          'postgres_changes',
          {
            event: 'INSERT',
            schema: 'public',
            table: 'messages',
            filter: `conversation_id=eq.${conversationId}`,
          },
          (payload) => {
            const newMessage = payload.new as Message;
            addMessage(conversationId, newMessage);
          }
        )
        .on(
          'postgres_changes',
          {
            event: 'UPDATE',
            schema: 'public',
            table: 'messages',
            filter: `conversation_id=eq.${conversationId}`,
          },
          (payload) => {
            const updatedMessage = payload.new as Message;
            updateMessage(conversationId, updatedMessage);
          }
        )
        .subscribe((status) => {
          setIsConnected(status === 'SUBSCRIBED');
        });

      return () => {
        supabase.removeChannel(channel);
      };
    }, [conversationId, addMessage, updateMessage]);

    return { isConnected };
  }
  ```

**Task 3.2: Typing Indicators Hook**
- **File**: `src/modules/chat/hooks/use-typing-indicator.ts`
  ```typescript
  import { useEffect, useState, useCallback } from 'react';
  import { createClient } from '@/lib/supabase/client';
  import { CHAT_CONFIG } from '../constants';

  export function useTypingIndicator(conversationId: string, userId: string) {
    const [typingUsers, setTypingUsers] = useState<string[]>([]);
    const [isTyping, setIsTyping] = useState(false);

    const startTyping = useCallback(() => {
      if (!isTyping) {
        setIsTyping(true);
        const supabase = createClient();
        supabase.channel(`typing:${conversationId}`)
          .send({
            type: 'broadcast',
            event: 'typing',
            payload: { user_id: userId, typing: true }
          });
      }
    }, [conversationId, userId, isTyping]);

    const stopTyping = useCallback(() => {
      if (isTyping) {
        setIsTyping(false);
        const supabase = createClient();
        supabase.channel(`typing:${conversationId}`)
          .send({
            type: 'broadcast',
            event: 'typing',
            payload: { user_id: userId, typing: false }
          });
      }
    }, [conversationId, userId, isTyping]);

    useEffect(() => {
      const supabase = createClient();

      const channel = supabase
        .channel(`typing:${conversationId}`)
        .on('broadcast', { event: 'typing' }, (payload) => {
          const { user_id, typing } = payload.payload;
          if (user_id !== userId) {
            setTypingUsers(prev =>
              typing
                ? [...prev.filter(id => id !== user_id), user_id]
                : prev.filter(id => id !== user_id)
            );
          }
        })
        .subscribe();

      return () => {
        supabase.removeChannel(channel);
      };
    }, [conversationId, userId]);

    // Auto-stop typing after timeout
    useEffect(() => {
      if (isTyping) {
        const timeout = setTimeout(stopTyping, CHAT_CONFIG.TYPING_TIMEOUT);
        return () => clearTimeout(timeout);
      }
    }, [isTyping, stopTyping]);

    return {
      typingUsers,
      startTyping,
      stopTyping,
    };
  }
  ```

#### **Day 4: Chat State Management (Zustand)**

**Task 4.1: Chat Store Implementation**
- **File**: `src/modules/chat/store/chat-store.ts`
  ```typescript
  import { create } from 'zustand';
  import { Message, ConversationDetails } from '../types';
  import { ChatClient } from '../api';

  interface ChatState {
    // State
    conversations: Record<string, ConversationDetails>;
    messages: Record<string, Message[]>;
    loading: Record<string, boolean>;
    error: string | null;
    currentConversationId: string | null;

    // Actions
    setCurrentConversation: (id: string) => void;
    loadMessages: (conversationId: string) => Promise<void>;
    addMessage: (conversationId: string, message: Message) => void;
    updateMessage: (conversationId: string, message: Message) => void;
    sendMessage: (conversationId: string, content: string, type?: 'text' | 'file' | 'image') => Promise<void>;
    clearError: () => void;
  }

  export const useChatStore = create<ChatState>((set, get) => ({
    conversations: {},
    messages: {},
    loading: {},
    error: null,
    currentConversationId: null,

    setCurrentConversation: (id) => {
      set({ currentConversationId: id });
    },

    loadMessages: async (conversationId) => {
      set(state => ({
        loading: { ...state.loading, [conversationId]: true }
      }));

      try {
        const { data, error } = await ChatClient.getMessages(conversationId);

        if (error) {
          set({ error });
          return;
        }

        set(state => ({
          messages: { ...state.messages, [conversationId]: data || [] },
          loading: { ...state.loading, [conversationId]: false },
          error: null,
        }));
      } catch (error) {
        set(state => ({
          loading: { ...state.loading, [conversationId]: false },
          error: 'Failed to load messages',
        }));
      }
    },

    addMessage: (conversationId, message) => {
      set(state => ({
        messages: {
          ...state.messages,
          [conversationId]: [message, ...(state.messages[conversationId] || [])]
        }
      }));
    },

    updateMessage: (conversationId, updatedMessage) => {
      set(state => ({
        messages: {
          ...state.messages,
          [conversationId]: (state.messages[conversationId] || []).map(msg =>
            msg.id === updatedMessage.id ? updatedMessage : msg
          )
        }
      }));
    },

    sendMessage: async (conversationId, content, type = 'text') => {
      try {
        const { data, error } = await ChatClient.sendMessage({
          conversation_id: conversationId,
          content,
          message_type: type,
        });

        if (error) {
          set({ error });
          return;
        }

        // Message will be added via real-time subscription
      } catch (error) {
        set({ error: 'Failed to send message' });
      }
    },

    clearError: () => set({ error: null }),
  }));

  // Individual selectors for reactive state
  export const useChatMessages = (conversationId: string) =>
    useChatStore(state => state.messages[conversationId] || []);

  export const useChatLoading = (conversationId: string) =>
    useChatStore(state => state.loading[conversationId] || false);

  export const useChatError = () =>
    useChatStore(state => state.error);

  export const useCurrentConversationId = () =>
    useChatStore(state => state.currentConversationId);
  ```

**Task 4.2: Main Chat Hook**
- **File**: `src/modules/chat/hooks/use-chat.ts`
  ```typescript
  import { useEffect } from 'react';
  import {
    useChatMessages,
    useChatLoading,
    useChatError,
    useChatStore
  } from '../store';
  import { useRealtimeMessages } from './use-realtime-messages';
  import { useTypingIndicator } from './use-typing-indicator';

  export function useChat(conversationId: string, userId: string) {
    // Reactive state
    const messages = useChatMessages(conversationId);
    const loading = useChatLoading(conversationId);
    const error = useChatError();

    // Direct action access
    const {
      loadMessages,
      sendMessage,
      setCurrentConversation,
      clearError
    } = useChatStore.getState();

    // Real-time subscriptions
    const { isConnected } = useRealtimeMessages(conversationId);
    const { typingUsers, startTyping, stopTyping } = useTypingIndicator(conversationId, userId);

    // Load messages on mount
    useEffect(() => {
      if (conversationId && !messages.length && !loading) {
        loadMessages(conversationId);
      }
    }, [conversationId, messages.length, loading, loadMessages]);

    // Set current conversation
    useEffect(() => {
      setCurrentConversation(conversationId);
    }, [conversationId, setCurrentConversation]);

    return {
      messages,
      loading,
      error,
      isConnected,
      typingUsers,
      sendMessage: (content: string) => sendMessage(conversationId, content),
      startTyping,
      stopTyping,
      clearError,
    };
  }
  ```

---

### **Phase 3: Core Chat UI Components (Days 5-7)**

#### **Day 5: Chat Layout & Message List**

**Task 5.1: Chat Layout Component**
- **File**: `src/modules/chat/components/layout/chat-layout.tsx`
  ```typescript
  'use client';

  import { ReactNode } from 'react';
  import { ChatHeader } from './chat-header';
  import { MessageList } from '../messages/message-list';
  import { MessageInput } from '../input/message-input';
  import { TypingIndicator } from '../messages/typing-indicator';
  import { useChat } from '../../hooks/use-chat';
  import { useAuthStore } from '@/stores/auth-store';

  interface ChatLayoutProps {
    conversationId: string;
    children?: ReactNode;
  }

  export function ChatLayout({ conversationId }: ChatLayoutProps) {
    const { user } = useAuthStore.getState();
    const {
      messages,
      loading,
      error,
      isConnected,
      typingUsers,
      sendMessage,
      startTyping,
      stopTyping,
      clearError,
    } = useChat(conversationId, user?.userId || '');

    if (loading && !messages.length) {
      return (
        <div className="flex h-screen items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2"></div>
            <p className="text-gray-600">Loading chat...</p>
          </div>
        </div>
      );
    }

    return (
      <div className="flex flex-col h-screen bg-gray-50">
        {/* Header */}
        <ChatHeader
          conversationId={conversationId}
          isConnected={isConnected}
        />

        {/* Error Banner */}
        {error && (
          <div className="bg-red-50 border-l-4 border-red-400 p-4">
            <div className="flex justify-between items-center">
              <p className="text-red-700">{error}</p>
              <button
                onClick={clearError}
                className="text-red-400 hover:text-red-600"
              >
                ×
              </button>
            </div>
          </div>
        )}

        {/* Messages */}
        <div className="flex-1 overflow-hidden">
          <MessageList
            messages={messages}
            currentUserId={user?.userId || ''}
            loading={loading}
          />
        </div>

        {/* Typing Indicator */}
        {typingUsers.length > 0 && (
          <TypingIndicator userIds={typingUsers} />
        )}

        {/* Message Input */}
        <MessageInput
          onSendMessage={sendMessage}
          onStartTyping={startTyping}
          onStopTyping={stopTyping}
          disabled={!isConnected}
        />
      </div>
    );
  }
  ```

**Task 5.2: Chat Header Component**
- **File**: `src/modules/chat/components/layout/chat-header.tsx`
  ```typescript
  'use client';

  import { ArrowLeft, Users, Phone, Video, MoreVertical } from 'lucide-react';
  import { Button } from '@/components/ui/button';
  import { useRouter } from 'next/navigation';
  import { useState, useEffect } from 'react';
  import { ConversationDetails } from '../../types';
  import { ChatClient } from '../../api';

  interface ChatHeaderProps {
    conversationId: string;
    isConnected: boolean;
  }

  export function ChatHeader({ conversationId, isConnected }: ChatHeaderProps) {
    const router = useRouter();
    const [conversation, setConversation] = useState<ConversationDetails | null>(null);

    useEffect(() => {
      const loadConversation = async () => {
        const { data } = await ChatClient.getConversationDetails(conversationId);
        if (data) setConversation(data);
      };
      loadConversation();
    }, [conversationId]);

    const getHeaderTitle = () => {
      if (!conversation) return 'Loading...';

      if (conversation.type === 'group') {
        return conversation.subject_id ? 'Group Chat' : 'Group';
      }

      // For direct chats, show other participant's name
      const otherParticipant = conversation.participants.find(p => p.user_id !== conversation.participants[0].user_id);
      return otherParticipant
        ? `${otherParticipant.user.first_name} ${otherParticipant.user.last_name}`
        : 'Direct Chat';
    };

    const getSubtitle = () => {
      if (!conversation) return '';

      if (conversation.type === 'group') {
        return `${conversation.participants.length} participants`;
      }

      return isConnected ? 'Online' : 'Connecting...';
    };

    return (
      <div className="bg-white border-b border-gray-200 px-4 py-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            {/* Back Button */}
            <Button
              variant="ghost"
              size="icon"
              onClick={() => router.back()}
              className="md:hidden"
            >
              <ArrowLeft className="h-5 w-5" />
            </Button>

            {/* Conversation Info */}
            <div className="flex items-center space-x-3">
              <div className="relative">
                <div className="w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center">
                  {conversation?.type === 'group' ? (
                    <Users className="h-5 w-5 text-white" />
                  ) : (
                    <div className="w-8 h-8 bg-gray-300 rounded-full"></div>
                  )}
                </div>
                {isConnected && (
                  <div className="absolute -bottom-1 -right-1 w-3 h-3 bg-green-500 rounded-full border-2 border-white"></div>
                )}
              </div>

              <div>
                <h1 className="font-semibold text-gray-900">{getHeaderTitle()}</h1>
                <p className="text-sm text-gray-500">{getSubtitle()}</p>
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex items-center space-x-2">
            <Button variant="ghost" size="icon" className="hidden md:flex">
              <Phone className="h-5 w-5" />
            </Button>
            <Button variant="ghost" size="icon" className="hidden md:flex">
              <Video className="h-5 w-5" />
            </Button>
            <Button variant="ghost" size="icon">
              <MoreVertical className="h-5 w-5" />
            </Button>
          </div>
        </div>
      </div>
    );
  }
  ```

**Task 5.3: Message List Component**
- **File**: `src/modules/chat/components/messages/message-list.tsx`
  ```typescript
  'use client';

  import { useEffect, useRef } from 'react';
  import { Message } from '../../types';
  import { MessageBubble } from './message-bubble';
  import { DateSeparator } from './date-separator';
  import { LoadingSpinner } from '../common/loading-spinner';

  interface MessageListProps {
    messages: Message[];
    currentUserId: string;
    loading: boolean;
  }

  export function MessageList({ messages, currentUserId, loading }: MessageListProps) {
    const messagesEndRef = useRef<HTMLDivElement>(null);
    const containerRef = useRef<HTMLDivElement>(null);

    // Auto-scroll to bottom for new messages
    useEffect(() => {
      if (messagesEndRef.current) {
        messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
      }
    }, [messages]);

    const groupMessagesByDate = (messages: Message[]) => {
      const groups: { date: string; messages: Message[] }[] = [];
      let currentDate = '';

      messages.forEach(message => {
        const messageDate = new Date(message.created_at).toDateString();

        if (messageDate !== currentDate) {
          currentDate = messageDate;
          groups.push({ date: messageDate, messages: [message] });
        } else {
          groups[groups.length - 1].messages.push(message);
        }
      });

      return groups;
    };

    const messageGroups = groupMessagesByDate([...messages].reverse());

    if (loading && !messages.length) {
      return (
        <div className="flex-1 flex items-center justify-center">
          <LoadingSpinner />
        </div>
      );
    }

    if (!messages.length) {
      return (
        <div className="flex-1 flex items-center justify-center">
          <div className="text-center text-gray-500">
            <p>No messages yet</p>
            <p className="text-sm">Start the conversation!</p>
          </div>
        </div>
      );
    }

    return (
      <div
        ref={containerRef}
        className="flex-1 overflow-y-auto px-4 py-4 space-y-4"
      >
        {messageGroups.map((group, groupIndex) => (
          <div key={group.date}>
            <DateSeparator date={group.date} />

            <div className="space-y-2">
              {group.messages.map((message, messageIndex) => {
                const isOwn = message.sender_id === currentUserId;
                const showAvatar = !isOwn && (
                  messageIndex === group.messages.length - 1 ||
                  group.messages[messageIndex + 1]?.sender_id !== message.sender_id
                );

                return (
                  <MessageBubble
                    key={message.id}
                    message={message}
                    isOwn={isOwn}
                    showAvatar={showAvatar}
                  />
                );
              })}
            </div>
          </div>
        ))}

        <div ref={messagesEndRef} />
      </div>
    );
  }
  ```

#### **Day 6: Message Components**

**Task 6.1: Message Bubble Component**
- **File**: `src/modules/chat/components/messages/message-bubble.tsx`
  ```typescript
  'use client';

  import { Message } from '../../types';
  import { formatMessageTime } from '../../utils/date-utils';
  import { FileAttachment } from './file-attachment';
  import { ImageAttachment } from './image-attachment';

  interface MessageBubbleProps {
    message: Message;
    isOwn: boolean;
    showAvatar: boolean;
  }

  export function MessageBubble({ message, isOwn, showAvatar }: MessageBubbleProps) {
    const renderMessageContent = () => {
      switch (message.message_type) {
        case 'text':
          return (
            <p className="text-sm whitespace-pre-wrap break-words">
              {message.content}
            </p>
          );

        case 'image':
          return (
            <div>
              {message.attachments?.map(attachment => (
                <ImageAttachment
                  key={attachment.id}
                  attachment={attachment}
                />
              ))}
              {message.content && (
                <p className="text-sm mt-2 whitespace-pre-wrap break-words">
                  {message.content}
                </p>
              )}
            </div>
          );

        case 'file':
          return (
            <div>
              {message.attachments?.map(attachment => (
                <FileAttachment
                  key={attachment.id}
                  attachment={attachment}
                />
              ))}
              {message.content && (
                <p className="text-sm mt-2 whitespace-pre-wrap break-words">
                  {message.content}
                </p>
              )}
            </div>
          );

        default:
          return <p className="text-sm text-gray-500">Unsupported message type</p>;
      }
    };

    return (
      <div className={`flex ${isOwn ? 'justify-end' : 'justify-start'} mb-2`}>
        <div className={`flex max-w-xs lg:max-w-md ${isOwn ? 'flex-row-reverse' : 'flex-row'}`}>
          {/* Avatar */}
          {showAvatar && !isOwn && (
            <div className="w-8 h-8 rounded-full bg-gray-300 flex-shrink-0 mr-2 flex items-center justify-center">
              {message.sender?.avatar_url ? (
                <img
                  src={message.sender.avatar_url}
                  alt={`${message.sender.first_name} ${message.sender.last_name}`}
                  className="w-8 h-8 rounded-full object-cover"
                />
              ) : (
                <span className="text-xs font-medium text-gray-600">
                  {message.sender?.first_name?.[0]}{message.sender?.last_name?.[0]}
                </span>
              )}
            </div>
          )}

          {/* Message Content */}
          <div className={`${showAvatar && !isOwn ? '' : 'ml-10'}`}>
            {/* Sender Name (for group chats) */}
            {!isOwn && showAvatar && (
              <p className="text-xs text-gray-500 mb-1">
                {message.sender?.first_name} {message.sender?.last_name}
              </p>
            )}

            {/* Message Bubble */}
            <div
              className={`
                px-4 py-2 rounded-lg
                ${isOwn
                  ? 'bg-blue-600 text-white'
                  : 'bg-white border border-gray-200 text-gray-900'
                }
              `}
            >
              {renderMessageContent()}

              {/* Timestamp */}
              <p className={`text-xs mt-1 ${isOwn ? 'text-blue-100' : 'text-gray-500'}`}>
                {formatMessageTime(message.created_at)}
              </p>
            </div>
          </div>
        </div>
      </div>
    );
  }
  ```

**Task 6.2: Supporting Components**
- **File**: `src/modules/chat/components/messages/date-separator.tsx`
  ```typescript
  import { formatDateSeparator } from '../../utils/date-utils';

  interface DateSeparatorProps {
    date: string;
  }

  export function DateSeparator({ date }: DateSeparatorProps) {
    return (
      <div className="flex items-center justify-center my-4">
        <div className="bg-gray-100 px-3 py-1 rounded-full">
          <span className="text-xs text-gray-600 font-medium">
            {formatDateSeparator(date)}
          </span>
        </div>
      </div>
    );
  }
  ```

- **File**: `src/modules/chat/components/messages/typing-indicator.tsx`
  ```typescript
  'use client';

  import { useEffect, useState } from 'react';

  interface TypingIndicatorProps {
    userIds: string[];
  }

  export function TypingIndicator({ userIds }: TypingIndicatorProps) {
    const [dots, setDots] = useState('');

    useEffect(() => {
      const interval = setInterval(() => {
        setDots(prev => prev.length >= 3 ? '' : prev + '.');
      }, 500);

      return () => clearInterval(interval);
    }, []);

    if (userIds.length === 0) return null;

    const getTypingText = () => {
      if (userIds.length === 1) {
        return `Someone is typing${dots}`;
      }
      return `${userIds.length} people are typing${dots}`;
    };

    return (
      <div className="px-4 py-2 bg-gray-50 border-t border-gray-200">
        <div className="flex items-center space-x-2">
          <div className="flex space-x-1">
            <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
            <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
            <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
          </div>
          <span className="text-sm text-gray-600">{getTypingText()}</span>
        </div>
      </div>
    );
  }
  ```

#### **Day 7: Message Input & File Upload**

**Task 7.1: Message Input Component**
- **File**: `src/modules/chat/components/input/message-input.tsx`
  ```typescript
  'use client';

  import { useState, useRef, KeyboardEvent } from 'react';
  import { Button } from '@/components/ui/button';
  import { Textarea } from '@/components/ui/textarea';
  import { Send, Paperclip, Image, Smile } from 'lucide-react';
  import { FileUploadButton } from './file-upload-button';

  interface MessageInputProps {
    onSendMessage: (content: string) => Promise<void>;
    onStartTyping: () => void;
    onStopTyping: () => void;
    disabled?: boolean;
  }

  export function MessageInput({
    onSendMessage,
    onStartTyping,
    onStopTyping,
    disabled = false
  }: MessageInputProps) {
    const [message, setMessage] = useState('');
    const [isSending, setIsSending] = useState(false);
    const textareaRef = useRef<HTMLTextAreaElement>(null);
    const typingTimeoutRef = useRef<NodeJS.Timeout>();

    const handleSend = async () => {
      const trimmedMessage = message.trim();
      if (!trimmedMessage || isSending || disabled) return;

      setIsSending(true);
      try {
        await onSendMessage(trimmedMessage);
        setMessage('');
        onStopTyping();

        // Reset textarea height
        if (textareaRef.current) {
          textareaRef.current.style.height = 'auto';
        }
      } catch (error) {
        console.error('Failed to send message:', error);
      } finally {
        setIsSending(false);
      }
    };

    const handleKeyPress = (e: KeyboardEvent<HTMLTextAreaElement>) => {
      if (e.key === 'Enter' && !e.shiftKey) {
        e.preventDefault();
        handleSend();
      }
    };

    const handleInputChange = (value: string) => {
      setMessage(value);

      // Handle typing indicators
      if (value.trim()) {
        onStartTyping();

        // Clear existing timeout
        if (typingTimeoutRef.current) {
          clearTimeout(typingTimeoutRef.current);
        }

        // Set new timeout to stop typing
        typingTimeoutRef.current = setTimeout(() => {
          onStopTyping();
        }, 1000);
      } else {
        onStopTyping();
      }

      // Auto-resize textarea
      if (textareaRef.current) {
        textareaRef.current.style.height = 'auto';
        textareaRef.current.style.height = `${textareaRef.current.scrollHeight}px`;
      }
    };

    return (
      <div className="bg-white border-t border-gray-200 p-4">
        <div className="flex items-end space-x-2">
          {/* File Upload */}
          <div className="flex space-x-1">
            <FileUploadButton
              onFileSelect={(file) => {
                // Handle file upload
                console.log('File selected:', file);
              }}
              accept="image/*"
              disabled={disabled}
            >
              <Image className="h-5 w-5" />
            </FileUploadButton>

            <FileUploadButton
              onFileSelect={(file) => {
                // Handle file upload
                console.log('File selected:', file);
              }}
              disabled={disabled}
            >
              <Paperclip className="h-5 w-5" />
            </FileUploadButton>
          </div>

          {/* Message Input */}
          <div className="flex-1 relative">
            <Textarea
              ref={textareaRef}
              value={message}
              onChange={(e) => handleInputChange(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="Type a message..."
              disabled={disabled || isSending}
              className="min-h-[40px] max-h-32 resize-none pr-12"
              rows={1}
            />

            {/* Emoji Button */}
            <Button
              variant="ghost"
              size="icon"
              className="absolute right-2 bottom-2 h-6 w-6"
              disabled={disabled}
            >
              <Smile className="h-4 w-4" />
            </Button>
          </div>

          {/* Send Button */}
          <Button
            onClick={handleSend}
            disabled={!message.trim() || isSending || disabled}
            size="icon"
            className="h-10 w-10"
          >
            <Send className="h-4 w-4" />
          </Button>
        </div>
      </div>
    );
  }
  ```

---

### **Phase 4: File Upload System & API Routes (Days 8-9)**

#### **Day 8: File Upload Implementation**

**Task 8.1: File Upload Components**
- **File**: `src/modules/chat/components/input/file-upload-button.tsx`
  ```typescript
  'use client';

  import { useRef, ReactNode } from 'react';
  import { Button } from '@/components/ui/button';
  import { CHAT_CONFIG } from '../../constants';

  interface FileUploadButtonProps {
    onFileSelect: (file: File) => void;
    accept?: string;
    disabled?: boolean;
    children: ReactNode;
  }

  export function FileUploadButton({
    onFileSelect,
    accept = "*/*",
    disabled = false,
    children
  }: FileUploadButtonProps) {
    const fileInputRef = useRef<HTMLInputElement>(null);

    const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
      const file = event.target.files?.[0];
      if (!file) return;

      // Validate file size
      if (file.size > CHAT_CONFIG.MAX_FILE_SIZE) {
        alert(`File size must be less than ${CHAT_CONFIG.MAX_FILE_SIZE / (1024 * 1024)}MB`);
        return;
      }

      // Validate file type
      if (!CHAT_CONFIG.ALLOWED_FILE_TYPES.includes(file.type)) {
        alert('File type not supported');
        return;
      }

      onFileSelect(file);

      // Reset input
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    };

    return (
      <>
        <input
          ref={fileInputRef}
          type="file"
          accept={accept}
          onChange={handleFileSelect}
          className="hidden"
          disabled={disabled}
        />
        <Button
          variant="ghost"
          size="icon"
          onClick={() => fileInputRef.current?.click()}
          disabled={disabled}
          className="h-10 w-10"
        >
          {children}
        </Button>
      </>
    );
  }
  ```

**Task 8.2: File Upload API**
- **File**: `src/modules/chat/api/file-upload-client.ts`
  ```typescript
  import { CHAT_CONFIG } from '../constants';

  export interface FileUploadProgress {
    loaded: number;
    total: number;
    percentage: number;
  }

  export interface FileUploadResult {
    file_url: string;
    file_name: string;
    file_type: string;
    file_size: number;
  }

  export class FileUploadClient {
    static async uploadFile(
      file: File,
      conversationId: string,
      onProgress?: (progress: FileUploadProgress) => void
    ): Promise<{ data: FileUploadResult | null; error: string | null }> {
      try {
        // Validate file
        if (file.size > CHAT_CONFIG.MAX_FILE_SIZE) {
          return { data: null, error: 'File too large' };
        }

        if (!CHAT_CONFIG.ALLOWED_FILE_TYPES.includes(file.type)) {
          return { data: null, error: 'File type not supported' };
        }

        const formData = new FormData();
        formData.append('file', file);
        formData.append('conversation_id', conversationId);

        const response = await fetch('/api/chat/upload', {
          method: 'POST',
          body: formData,
          credentials: 'include',
        });

        if (!response.ok) {
          const errorData = await response.json();
          return { data: null, error: errorData.error || 'Upload failed' };
        }

        const result = await response.json();
        return { data: result, error: null };
      } catch (error) {
        console.error('File upload error:', error);
        return { data: null, error: 'Upload failed' };
      }
    }
  }
  ```

**Task 8.3: File Upload Server API**
- **File**: `src/modules/chat/api/file-upload-server-api.ts`
  ```typescript
  import { createClient } from '@/lib/supabase/server';
  import { CHAT_CONFIG } from '../constants';

  export async function uploadChatFile(
    file: File,
    conversationId: string,
    userId: string
  ): Promise<{ data: any | null; error: string | null }> {
    try {
      const supabase = await createClient();

      // Verify user is participant in conversation
      const { data: participant } = await supabase
        .from('conversation_participants')
        .select('id')
        .eq('conversation_id', conversationId)
        .eq('user_id', userId)
        .single();

      if (!participant) {
        return { data: null, error: 'Access denied' };
      }

      // Generate unique filename
      const fileExt = file.name.split('.').pop();
      const fileName = `${Date.now()}-${Math.random().toString(36).substring(2)}.${fileExt}`;
      const filePath = `chat-files/${conversationId}/${fileName}`;

      // Upload to Supabase Storage
      const { data: uploadData, error: uploadError } = await supabase.storage
        .from('chat-files')
        .upload(filePath, file, {
          cacheControl: '3600',
          upsert: false
        });

      if (uploadError) {
        return { data: null, error: uploadError.message };
      }

      // Get public URL
      const { data: urlData } = supabase.storage
        .from('chat-files')
        .getPublicUrl(filePath);

      return {
        data: {
          file_url: urlData.publicUrl,
          file_name: file.name,
          file_type: file.type,
          file_size: file.size,
          storage_path: filePath,
        },
        error: null,
      };
    } catch (error) {
      console.error('File upload error:', error);
      return { data: null, error: 'Upload failed' };
    }
  }
  ```

#### **Day 9: Message API Routes & File Attachments**

**Task 9.1: Message API Routes**
- **File**: `src/app/api/chat/messages/[conversationId]/route.ts`
  ```typescript
  import { NextRequest, NextResponse } from 'next/server';
  import { withAuth } from '@/lib/api-auth';
  import { getMessages } from '@/modules/chat/api/message-server-api';
  import { getMessagesSchema } from '@/modules/chat/schemas';

  export const GET = withAuth(async (request: NextRequest, { user, params }) => {
    try {
      const conversationId = params.conversationId as string;
      const { searchParams } = new URL(request.url);

      const queryParams = {
        conversation_id: conversationId,
        limit: parseInt(searchParams.get('limit') || '50'),
        offset: parseInt(searchParams.get('offset') || '0'),
        before_id: searchParams.get('before_id') || undefined,
      };

      const validation = getMessagesSchema.safeParse(queryParams);
      if (!validation.success) {
        return NextResponse.json(
          { error: 'Invalid parameters', details: validation.error.issues },
          { status: 400 }
        );
      }

      const { data, error } = await getMessages(
        conversationId,
        user.userId,
        validation.data.limit,
        validation.data.offset
      );

      if (error) {
        return NextResponse.json({ error }, { status: 500 });
      }

      return NextResponse.json(data);
    } catch (error) {
      console.error('Get messages error:', error);
      return NextResponse.json(
        { error: 'Internal server error' },
        { status: 500 }
      );
    }
  });
  ```

- **File**: `src/app/api/chat/messages/send/route.ts`
  ```typescript
  import { NextRequest, NextResponse } from 'next/server';
  import { withAuth } from '@/lib/api-auth';
  import { sendMessage } from '@/modules/chat/api/message-server-api';
  import { sendMessageSchema } from '@/modules/chat/schemas';

  export const POST = withAuth(async (request: NextRequest, { user }) => {
    try {
      const body = await request.json();

      const validation = sendMessageSchema.safeParse(body);
      if (!validation.success) {
        return NextResponse.json(
          { error: 'Invalid request data', details: validation.error.issues },
          { status: 400 }
        );
      }

      const { data, error } = await sendMessage(validation.data, user.userId);

      if (error) {
        return NextResponse.json({ error }, { status: 500 });
      }

      return NextResponse.json(data);
    } catch (error) {
      console.error('Send message error:', error);
      return NextResponse.json(
        { error: 'Internal server error' },
        { status: 500 }
      );
    }
  });
  ```

- **File**: `src/app/api/chat/upload/route.ts`
  ```typescript
  import { NextRequest, NextResponse } from 'next/server';
  import { withAuth } from '@/lib/api-auth';
  import { uploadChatFile } from '@/modules/chat/api/file-upload-server-api';

  export const POST = withAuth(async (request: NextRequest, { user }) => {
    try {
      const formData = await request.formData();
      const file = formData.get('file') as File;
      const conversationId = formData.get('conversation_id') as string;

      if (!file || !conversationId) {
        return NextResponse.json(
          { error: 'File and conversation_id are required' },
          { status: 400 }
        );
      }

      const { data, error } = await uploadChatFile(file, conversationId, user.userId);

      if (error) {
        return NextResponse.json({ error }, { status: 500 });
      }

      return NextResponse.json(data);
    } catch (error) {
      console.error('File upload error:', error);
      return NextResponse.json(
        { error: 'Internal server error' },
        { status: 500 }
      );
    }
  });
  ```

**Task 9.2: File Attachment Components**
- **File**: `src/modules/chat/components/messages/file-attachment.tsx`
  ```typescript
  'use client';

  import { Download, FileText, File } from 'lucide-react';
  import { Button } from '@/components/ui/button';
  import { MessageAttachment } from '../../types';
  import { formatFileSize } from '../../utils/file-utils';

  interface FileAttachmentProps {
    attachment: MessageAttachment;
  }

  export function FileAttachment({ attachment }: FileAttachmentProps) {
    const handleDownload = () => {
      const link = document.createElement('a');
      link.href = attachment.file_url;
      link.download = attachment.file_name;
      link.target = '_blank';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    };

    const getFileIcon = () => {
      if (attachment.file_type.includes('pdf')) {
        return <FileText className="h-8 w-8 text-red-500" />;
      }
      return <File className="h-8 w-8 text-gray-500" />;
    };

    return (
      <div className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg border">
        {getFileIcon()}

        <div className="flex-1 min-w-0">
          <p className="text-sm font-medium text-gray-900 truncate">
            {attachment.file_name}
          </p>
          <p className="text-xs text-gray-500">
            {formatFileSize(attachment.file_size)}
          </p>
        </div>

        <Button
          variant="ghost"
          size="icon"
          onClick={handleDownload}
          className="h-8 w-8"
        >
          <Download className="h-4 w-4" />
        </Button>
      </div>
    );
  }
  ```

- **File**: `src/modules/chat/components/messages/image-attachment.tsx`
  ```typescript
  'use client';

  import { useState } from 'react';
  import { MessageAttachment } from '../../types';
  import { Dialog, DialogContent } from '@/components/ui/dialog';

  interface ImageAttachmentProps {
    attachment: MessageAttachment;
  }

  export function ImageAttachment({ attachment }: ImageAttachmentProps) {
    const [isOpen, setIsOpen] = useState(false);

    return (
      <>
        <div
          className="cursor-pointer rounded-lg overflow-hidden max-w-xs"
          onClick={() => setIsOpen(true)}
        >
          <img
            src={attachment.file_url}
            alt={attachment.file_name}
            className="w-full h-auto object-cover hover:opacity-90 transition-opacity"
            loading="lazy"
          />
        </div>

        <Dialog open={isOpen} onOpenChange={setIsOpen}>
          <DialogContent className="max-w-4xl max-h-[90vh] p-0">
            <img
              src={attachment.file_url}
              alt={attachment.file_name}
              className="w-full h-auto object-contain"
            />
          </DialogContent>
        </Dialog>
      </>
    );
  }
  ```

---

### **Phase 5: Chat Pages & Integration (Days 10-11)**

#### **Day 10: Chat Pages & Routing**

**Task 10.1: Main Chat Page**
- **File**: `src/modules/chat/pages/chat-page.tsx`
  ```typescript
  'use client';

  import { useEffect } from 'react';
  import { useRouter } from 'next/navigation';
  import { ChatLayout } from '../components/layout/chat-layout';
  import { useAuthStore } from '@/stores/auth-store';

  interface ChatPageProps {
    conversationId: string;
  }

  export function ChatPage({ conversationId }: ChatPageProps) {
    const router = useRouter();
    const { user } = useAuthStore.getState();

    useEffect(() => {
      if (!user) {
        router.push('/auth/login');
        return;
      }

      if (!conversationId) {
        router.push('/dashboard');
        return;
      }
    }, [user, conversationId, router]);

    if (!user || !conversationId) {
      return (
        <div className="flex h-screen items-center justify-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      );
    }

    return <ChatLayout conversationId={conversationId} />;
  }
  ```

**Task 10.2: App Router Integration**
- **File**: `src/app/chat/[conversationId]/page.tsx`
  ```typescript
  import { ChatPage } from '@/modules/chat/pages/chat-page';

  interface PageProps {
    params: {
      conversationId: string;
    };
  }

  export default function Page({ params }: PageProps) {
    return <ChatPage conversationId={params.conversationId} />;
  }

  export async function generateMetadata({ params }: PageProps) {
    return {
      title: 'Chat - College Platform',
      description: 'Real-time messaging with classmates',
    };
  }
  ```

**Task 10.3: Chat Client API**
- **File**: `src/modules/chat/api/chat-client.ts`
  ```typescript
  import apiClient from '@/lib/axios';
  import { Message, SendMessageRequest, ConversationDetails } from '../types';

  export class ChatClient {
    static async getMessages(
      conversationId: string,
      limit = 50,
      offset = 0
    ): Promise<{ data: Message[] | null; error: string | null }> {
      try {
        const response = await apiClient.get(`/chat/messages/${conversationId}`, {
          params: { limit, offset }
        });
        return { data: response.data, error: null };
      } catch (error) {
        console.error('Error fetching messages:', error);
        return { data: null, error: 'Failed to fetch messages' };
      }
    }

    static async sendMessage(
      request: SendMessageRequest
    ): Promise<{ data: Message | null; error: string | null }> {
      try {
        const response = await apiClient.post('/chat/messages/send', request);
        return { data: response.data, error: null };
      } catch (error) {
        console.error('Error sending message:', error);
        return { data: null, error: 'Failed to send message' };
      }
    }

    static async getConversationDetails(
      conversationId: string
    ): Promise<{ data: ConversationDetails | null; error: string | null }> {
      try {
        const response = await apiClient.get(`/conversations/${conversationId}`);
        return { data: response.data, error: null };
      } catch (error) {
        console.error('Error fetching conversation:', error);
        return { data: null, error: 'Failed to fetch conversation' };
      }
    }
  }
  ```

#### **Day 11: Utility Functions & Polish**

**Task 11.1: Utility Functions**
- **File**: `src/modules/chat/utils/date-utils.ts`
  ```typescript
  export function formatMessageTime(dateString: string): string {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);

    if (diffInHours < 24) {
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    } else if (diffInHours < 24 * 7) {
      return date.toLocaleDateString([], { weekday: 'short', hour: '2-digit', minute: '2-digit' });
    } else {
      return date.toLocaleDateString([], { month: 'short', day: 'numeric', hour: '2-digit', minute: '2-digit' });
    }
  }

  export function formatDateSeparator(dateString: string): string {
    const date = new Date(dateString);
    const now = new Date();
    const diffInDays = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60 * 24));

    if (diffInDays === 0) {
      return 'Today';
    } else if (diffInDays === 1) {
      return 'Yesterday';
    } else if (diffInDays < 7) {
      return date.toLocaleDateString([], { weekday: 'long' });
    } else {
      return date.toLocaleDateString([], { month: 'long', day: 'numeric', year: 'numeric' });
    }
  }
  ```

- **File**: `src/modules/chat/utils/file-utils.ts`
  ```typescript
  export function formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes';

    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  export function getFileTypeIcon(fileType: string): string {
    if (fileType.includes('image')) return '🖼️';
    if (fileType.includes('pdf')) return '📄';
    if (fileType.includes('word')) return '📝';
    if (fileType.includes('text')) return '📄';
    return '📎';
  }

  export function isImageFile(fileType: string): boolean {
    return fileType.startsWith('image/');
  }
  ```

**Task 11.2: Module Barrel Exports**
- **File**: `src/modules/chat/index.ts`
  ```typescript
  // Pages
  export { ChatPage } from './pages/chat-page';

  // Components
  export { ChatLayout } from './components/layout/chat-layout';
  export { MessageList } from './components/messages/message-list';
  export { MessageInput } from './components/input/message-input';

  // Hooks
  export { useChat } from './hooks/use-chat';
  export { useRealtimeMessages } from './hooks/use-realtime-messages';

  // Types
  export type { Message, ConversationDetails, MessageAttachment } from './types';

  // API
  export { ChatClient } from './api/chat-client';

  // Store
  export { useChatStore, useChatMessages, useChatLoading } from './store/chat-store';
  ```

---

### **Phase 6: Testing & Performance Optimization (Day 12)**

#### **Day 12: Testing & Final Polish**

**Task 12.1: Error Handling & Loading States**
- Add comprehensive error boundaries
- Implement retry mechanisms for failed messages
- Add offline detection and queuing
- Optimize real-time reconnection logic

**Task 12.2: Performance Optimization**
- Implement message virtualization for large chat histories
- Add image lazy loading and optimization
- Optimize real-time subscription cleanup
- Add proper memory leak prevention

**Task 12.3: Mobile Responsiveness**
- Test touch gestures and mobile interactions
- Optimize keyboard behavior on mobile
- Add proper viewport handling
- Test file upload on mobile devices

**Task 12.4: Accessibility**
- Add proper ARIA labels and roles
- Implement keyboard navigation
- Add screen reader support
- Test with accessibility tools

---

## 🎯 Success Criteria & Validation

### **Technical Requirements**
- ✅ Real-time message delivery < 500ms
- ✅ File upload support up to 10MB
- ✅ Mobile-responsive design
- ✅ Offline message queuing
- ✅ Proper error handling and recovery

### **User Experience Requirements**
- ✅ WhatsApp-like chat interface
- ✅ Typing indicators and online status
- ✅ Image preview and file download
- ✅ Message history and pagination
- ✅ Smooth animations and transitions

### **Integration Requirements**
- ✅ Seamless navigation from subject pages
- ✅ Consistent with existing auth patterns
- ✅ Following established module structure
- ✅ Proper state management with Zustand
- ✅ Integration with existing conversation APIs

---

## 🚀 Deployment Checklist

### **Pre-deployment**
- [ ] All components tested in development
- [ ] Real-time functionality verified
- [ ] File upload tested with various file types
- [ ] Mobile responsiveness confirmed
- [ ] Performance benchmarks met

### **Production Setup**
- [ ] Supabase storage bucket configured
- [ ] Real-time subscriptions enabled
- [ ] File upload limits set
- [ ] Error monitoring configured
- [ ] Performance monitoring enabled

### **Post-deployment**
- [ ] End-to-end testing in production
- [ ] Real-time performance monitoring
- [ ] User feedback collection
- [ ] Bug tracking and resolution
- [ ] Performance optimization based on usage

---

This comprehensive plan builds upon the existing project architecture and leverages already-implemented conversation APIs, ensuring consistency with established patterns while delivering a complete, production-ready chat system that rivals modern messaging applications.
