{"name": "course-management", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build --turbopack", "start": "next start", "type-check": "npx tsc --noEmit"}, "dependencies": {"@hookform/resolvers": "^5.2.2", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-dialog": "^1.1.15", "@radix-ui/react-dropdown-menu": "^2.1.16", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-select": "^2.2.6", "@radix-ui/react-slot": "^1.2.3", "@supabase/ssr": "latest", "@supabase/supabase-js": "latest", "axios": "^1.12.2", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "input-otp": "^1.4.2", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.544.0", "next": "15.5.4", "next-themes": "^0.4.6", "nextjs-toploader": "^3.9.17", "react": "19.1.0", "react-dom": "19.1.0", "react-hook-form": "^7.63.0", "resend": "^6.1.0", "sonner": "^2.0.7", "tailwind-merge": "^3.3.1", "zod": "^4.1.11", "zustand": "^5.0.8"}, "devDependencies": {"@tailwindcss/postcss": "^4", "@types/bcryptjs": "^3.0.0", "@types/jsonwebtoken": "^9.0.10", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "tailwindcss": "^4", "tw-animate-css": "^1.4.0", "typescript": "^5"}, "packageManager": "pnpm@10.15.1+sha512.34e538c329b5553014ca8e8f4535997f96180a1d0f614339357449935350d924e22f8614682191264ec33d1462ac21561aff97f6bb18065351c162c7e8f6de67"}